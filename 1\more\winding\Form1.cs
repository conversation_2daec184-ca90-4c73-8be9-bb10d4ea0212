﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.IO;
using System.Linq;
using System.Reflection.Emit;
using System.Text;
using System.Threading.Tasks;
using CameraHandle = System.Int32;
using System.Threading;
using System.Windows.Forms;
using HalconDotNet;
using MvApi = MVSDK.MvApi;
using MVSDK;
using static MVSDK.MvApi;
using System.Runtime.InteropServices;
using System.Runtime.InteropServices.ComTypes;
// using static System.Net.Mime.MediaTypeNames; // 注释掉这行以避免Application命名冲突
using System.Windows.Forms.DataVisualization.Charting;
using System.Timers;
using FormsTimer = System.Windows.Forms.Timer;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Button;


namespace winding
{


    public partial class Form1 : Form
    {
        protected IntPtr m_Grabber = IntPtr.Zero;
        protected CameraHandle m_hCamera = 0;
        protected tSdkCameraDevInfo m_DevInfo;
        protected pfnCameraGrabberFrameCallback m_FrameCallback;
        protected pfnCameraGrabberSaveImageComplete m_SaveImageComplete;
        public HTuple hv_ExpDefaultWinHandle;
        private HObject currentImage; // 保存当前图像
        private readonly object imageLock = new object(); // 线程锁


        private static string logDirectoryPath = @"D:\save\log";
        private static string logFileNamePrefix = "检测日志_";
        private static string logFileNameSuffix = ".txt";
        string LogFilePath = GenerateUniqueLogFilePath(logDirectoryPath, logFileNamePrefix, logFileNameSuffix);
        //private string LogFilePath => Path.Combine(logDirectoryPath, $"{logFileNamePrefix}{DateTime.Now:yyyy-MM-dd}{logFileNameSuffix}");
        private FileSystemWatcher fileSystemWatcher;
        private ListView listViewLogs;
        public static pfnCameraSetFrameRate CameraSetFrameRate;
        public delegate CameraSdkStatus pfnCameraSetFrameRate(CameraHandle hCamera, int RateHZ);
        private bool isProcessing = false;
        private CancellationTokenSource cancellationTokenSource;
       // private System.Windows.Forms.Timer timer;

        // 添加一个备用日志目录路径
        private static string backupLogDirectoryPath = @"C:\temp\winding_logs";

        // 无限容量的图像队列 - 绝不丢弃图像
        private ConcurrentQueue<HObject> imageQueue = new ConcurrentQueue<HObject>();
        private int capturedFrames = 0; // 采集帧计数
        private int processedFrames = 0; // 处理帧计数
        private int queuedFrames = 0; // 队列中的帧数

        // 动态线程池管理
        private int processingThreads = Environment.ProcessorCount; // 初始线程数等于CPU核心数
        private int maxProcessingThreads = Environment.ProcessorCount * 3; // 最大线程数
        private List<Task> processingTasks = new List<Task>();

        // 实时性能监控
        private DateTime lastImageTime = DateTime.Now;
        private DateTime lastPerformanceCheck = DateTime.Now;
        private int lastProcessedCount = 0;
        private double currentProcessingRate = 0; // 当前处理速率（帧/秒）

        // 内存管理
        private long maxMemoryUsageMB = 4096; // 最大内存使用4GB
        private readonly object memoryLock = new object();

        private FormsTimer monitorTimer;

        // 使用高精度定时器触发采集
        private System.Timers.Timer captureTimer;

        public Form1()
        {
            InitializeComponent();
            InitializeLog();
            // 设置 ListView 列

            SetupListView();
            // 启动时检查并清理旧日志
            // CleanupOldLogs();
            chart1.Series.Clear();
            Series series = new Series("Mean Distance")
            {
                ChartType = SeriesChartType.Line
            };
            chart1.Series.Add(series);
            chart1.ChartAreas[0].AxisX.LabelStyle.Format = "HH:mm:ss"; // 横坐标格式化为时间
            chart1.ChartAreas[0].AxisX.Title = "Time";
            chart1.ChartAreas[0].AxisY.Title = "Mean Distance";



            hv_ExpDefaultWinHandle = hSmartWindowControl1.HalconWindow;

            m_FrameCallback = new pfnCameraGrabberFrameCallback(CameraGrabberFrameCallback);
            m_SaveImageComplete = new pfnCameraGrabberSaveImageComplete(CameraGrabberSaveImageComplete);


            if (MvApi.CameraGrabber_CreateFromDevicePage(out m_Grabber) == CameraSdkStatus.CAMERA_STATUS_SUCCESS)
            {
                MvApi.CameraGrabber_GetCameraDevInfo(m_Grabber, out m_DevInfo);
                MvApi.CameraGrabber_GetCameraHandle(m_Grabber, out m_hCamera);
                MvApi.CameraCreateSettingPage(m_hCamera, this.Handle, m_DevInfo.acFriendlyName, null, (IntPtr)0, 0);

                MvApi.CameraGrabber_SetRGBCallback(m_Grabber, m_FrameCallback, IntPtr.Zero);
                MvApi.CameraGrabber_SetSaveImageCompleteCallback(m_Grabber, m_SaveImageComplete, IntPtr.Zero);


                // 黑白相机设置ISP输出灰度图像
                // 彩色相机ISP默认会输出BGR24图像

                // 初始化GPIO 0为低电平
                // 设置低电平
                MvApi.CameraSetIOState(m_hCamera, 1, 1);
                if (rbLow.InvokeRequired)
                {
                    rbLow.Invoke(new Action(() => { rbLow.Checked = true; rbHigh.Checked = false; }));
                }
                else
                {
                    rbLow.Checked = true;
                    rbHigh.Checked = false;
                }




                tSdkCameraCapbility cap;
                MvApi.CameraGetCapability(m_hCamera, out cap);
                if (cap.sIspCapacity.bMonoSensor != 0)
                {
                    MvApi.CameraSetIspOutFormat(m_hCamera, (uint)MVSDK.emImageFormat.CAMERA_MEDIA_TYPE_MONO8);
                }

                MvApi.CameraGrabber_StartLive(m_Grabber);
                hSmartWindowControl1.MouseWheel += HSmartWindow_MouseWheel; //鼠标
            }


            listViewLogs = new ListView
            {
                Dock = DockStyle.Fill
            };
            this.Controls.Add(listViewLogs);


            // 初始化FileSystemWatcher
            fileSystemWatcher = new FileSystemWatcher();
            fileSystemWatcher.Path = @"D:\save\log"; // 设置日志文件所在的目录
            fileSystemWatcher.Filter = "*.log"; // 设置要监控的文件类型
            fileSystemWatcher.NotifyFilter = NotifyFilters.LastWrite; // 只监控文件的最后修改时间
                                                                      // fileSystemWatcher.Changed += FileSystemWatcher_Changed;
            fileSystemWatcher.EnableRaisingEvents = true; // 启用事件触发

            InitializeMonitoring();

            SetupSoftwareTrigger();
        }

        //鼠标滚轮回调
        private void HSmartWindow_MouseWheel(object sender, MouseEventArgs e)
        {
            //Point pt = this.Location;
            //MouseEventArgs newe = new MouseEventArgs(e.Button, e.Clicks, e.X - pt.X, e.Y - pt.Y, e.Delta);
            //hSmartWindowControl1.HSmartWindowControl_MouseWheel(sender, newe);

            System.Drawing.Point pt = this.Location;
            int leftBorder = hSmartWindowControl1.Location.X;
            int rightBorder = hSmartWindowControl1.Location.X + hSmartWindowControl1.Size.Width;
            int topBorder = hSmartWindowControl1.Location.Y;
            int bottomBorder = hSmartWindowControl1.Location.Y + hSmartWindowControl1.Size.Height;
            if (e.X > leftBorder && e.X < rightBorder && e.Y > topBorder && e.Y < bottomBorder)
            {
                MouseEventArgs newe = new MouseEventArgs(e.Button, e.Clicks, e.X - pt.X, e.Y - pt.Y, e.Delta);
                hSmartWindowControl1.HSmartWindowControl_MouseWheel(sender, newe);
            }

        }
        private void Timer1_Tick_1(object sender, EventArgs e)
        {

            if (m_Grabber != IntPtr.Zero)
            {
                MvApi.CameraGrabber_GetStat(m_Grabber, out tSdkGrabberStat stat);
                string info = String.Format("分辨率:{0}*{1} 帧数:{4} 显示帧率:{2:0.0} 采集帧率:{3:0.0}",
               stat.Width, stat.Height, stat.DispFps, stat.CapFps, stat.Capture);
                StateLabel.Text = info;
            }
        }

        private static string GenerateUniqueLogFilePath(string directoryPath, string fileNamePrefix, string fileNameSuffix)
        {
            string timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
            string uniqueId = Guid.NewGuid().ToString().Substring(0, 4); // 添加随机字符确保唯一性
            string uniqueFileName = $"{fileNamePrefix}{timestamp}_{uniqueId}{fileNameSuffix}";
            return Path.Combine(directoryPath, uniqueFileName);
        }


        // 初始化日志文件夹和文件
        private void InitializeLog()
        {
            try
            {
                // 尝试主日志目录
                if (!Directory.Exists(logDirectoryPath))
                {
                    try
                    {
                        Directory.CreateDirectory(logDirectoryPath);
                    }
                    catch (Exception)
                    {
                        // 如果主目录创建失败，尝试使用备用目录
                        if (!Directory.Exists(backupLogDirectoryPath))
                        {
                            Directory.CreateDirectory(backupLogDirectoryPath);
                        }
                        logDirectoryPath = backupLogDirectoryPath;
                        LogFilePath = GenerateUniqueLogFilePath(logDirectoryPath, logFileNamePrefix, logFileNameSuffix);
                    }
                }

                if (!File.Exists(LogFilePath))
                {
                    File.WriteAllText(LogFilePath, "绕线检测日志 - 检测状况\n\n");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"日志初始化失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void SetupListView()
        {
            listViewLog.View = View.Details;
            listViewLog.Columns.Add("时间", 140);
            listViewLog.Columns.Add("检测状况", 160);
            //listViewLog.AutoResizeColumns(ColumnHeaderAutoResizeStyle.HeaderSize);自动调整，效果不好
        }

        private async void LogMessage(string message)
        {
            int retryCount = 0;
            int maxRetries = 3;
            bool success = false;

            while (!success && retryCount < maxRetries)
            {
                try
                {
                    // 检查并清理旧日志
                    CleanupOldLogs();

                    // 创建日志条目
                    string logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss} - {message}";
                    
                    // 使用FileShare选项允许其他进程读取文件
                    using (var writer = new StreamWriter(new FileStream(LogFilePath, FileMode.Append, FileAccess.Write, FileShare.Read)))
                    {
                        await writer.WriteLineAsync(logEntry);
                        await writer.FlushAsync(); // 确保内容被写入
                    }

                    // 添加到 ListView
                    var item = new ListViewItem(DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"))
                    {
                        SubItems = { message }
                    };

                    if (listViewLog.InvokeRequired)
                    {
                        listViewLog.Invoke(new Action(() =>
                        {
                            AddLogToListView(item);
                        }));
                    }
                    else
                    {
                        AddLogToListView(item);
                    }

                    success = true; // 如果执行到此处，表示操作成功
                }
                catch (IOException ex) when (retryCount < maxRetries)
                {
                    retryCount++;
                    await Task.Delay(200 * retryCount); // 逐次增加延迟时间
                    
                    // 如果是因为文件被占用，可以尝试创建新的日志文件
                    if (ex.Message.Contains("正由另一进程使用") && retryCount == maxRetries - 1)
                    {
                        LogFilePath = GenerateUniqueLogFilePath(logDirectoryPath, logFileNamePrefix, logFileNameSuffix);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"日志记录失败: {ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    break; // 其他类型的异常，终止重试
                }
            }
        }

        private void AddLogToListView(ListViewItem item)
        {
            listViewLog.Items.Add(item);
            // 限制日志条目数量
            if (listViewLog.Items.Count > 5000)
            {
                listViewLog.Items.RemoveAt(0); // 限制日志条目数量
            }
            listViewLog.EnsureVisible(listViewLog.Items.Count - 1);
        }


        private void CleanupOldLogs()
        {
            try
            {
                var logFiles = Directory.GetFiles(logDirectoryPath, $"{logFileNamePrefix}*{logFileNameSuffix}");
                DateTime threshold = DateTime.Now.AddMonths(-1);

                foreach (var file in logFiles)
                {
                    FileInfo fileInfo = new FileInfo(file);
                    if (fileInfo.LastWriteTime < threshold)
                    {
                        fileInfo.Delete();
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"日志清理失败：{ex.Message}");
            }
        }


        private void Form1_Load(object sender, EventArgs e)
        {
          
        }

        private async void Camera_FormClosing(object sender, FormClosingEventArgs e)
        {
            // 如果这是用户发起的关闭操作，先取消关闭行为，然后异步执行清理
            if (e.CloseReason == CloseReason.UserClosing)
            {
                e.Cancel = true;
                
                LogMessage("开始关闭应用程序，正在安全清理所有资源...");

                // 显示正在关闭的信息
                Text = "正在安全关闭，请稍候...";
                Enabled = false;
                Application.DoEvents();

                // 异步执行清理
                await Task.Run(async () =>
                {
                    try
                    {
                        // 停止所有定时器
                        monitorTimer?.Stop();
                        captureTimer?.Stop();

                        // 停止处理线程 - 使用优雅停止方法
                        await GracefullyStopProcessingAsync(3);

                        // 反初始化相机
                        if (IntPtr.Zero != m_Grabber)
                        {
                            MvApi.CameraGrabber_Destroy(m_Grabber);
                            LogMessage("相机资源已释放");
                        }

                        // 清理剩余的队列中的图像 - 快速处理
                        int totalCleared = 0;
                        LogMessage("开始处理剩余队列中的图像...");

                        HObject remainingImage;
                        while (imageQueue.TryDequeue(out remainingImage))
                        {
                            remainingImage?.Dispose();
                            totalCleared++;

                            // 避免关闭时处理时间过长
                            if (totalCleared > 1000)
                            {
                                LogMessage("剩余图像过多，停止处理以避免关闭时间过长");
                                break;
                            }
                        }

                        // 清理当前图像
                        lock (imageLock)
                        {
                            currentImage?.Dispose();
                            currentImage = null;
                        }

                        LogMessage($"应用程序关闭完成。处理了{totalCleared}个剩余图像");
                        LogMessage($"最终统计：捕获{capturedFrames}帧，处理{processedFrames}帧");
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"关闭时清理资源出错: {ex.Message}");
                    }
                });

                // 真正关闭窗口
                BeginInvoke(new Action(() => 
                {
                    Application.Exit();
                }));
            }
        }
        private void CameraGrabberFrameCallback(
     IntPtr Grabber,
     IntPtr pFrameBuffer,
     ref tSdkFrameHead pFrameHead,
     IntPtr Context)
        {
            try
            {
                int w = pFrameHead.iWidth;
                int h = pFrameHead.iHeight;
                HObject Image = null;

                // 根据媒体类型生成HALCON图像对象
                if (pFrameHead.uiMediaType == (uint)MVSDK.emImageFormat.CAMERA_MEDIA_TYPE_MONO8)
                {
                    HOperatorSet.GenImage1(out Image, "byte", w, h, pFrameBuffer);
                }
                else if (pFrameHead.uiMediaType == (uint)MVSDK.emImageFormat.CAMERA_MEDIA_TYPE_BGR8)
                {
                    HOperatorSet.GenImageInterleaved(out Image,
                        pFrameBuffer,
                        "bgr",
                        w, h,
                        -1, "byte",
                        w, h,
                        0, 0, -1, 0);
                }

                if (Image != null)
                {
                    lock (imageLock)
                    {
                        // 安全地替换当前图像对象
                        currentImage?.Dispose(); // 释放旧图像对象
                        currentImage = Image;    // 设置新图像对象
                    }

                    // 安全地更新UI控件
                    if (hSmartWindowControl1.InvokeRequired)
                    {
                        hSmartWindowControl1.Invoke(new Action(() =>
                        {
                            DisplayImage(currentImage, hSmartWindowControl1);
                        }));
                    }
                    else
                    {
                        DisplayImage(currentImage, hSmartWindowControl1);

                    }

                    // 增加采集帧计数
                    Interlocked.Increment(ref capturedFrames);

                    // 无条件将图像添加到队列 - 绝不丢弃任何图像
                    try
                    {
                        // 创建图像副本添加到队列
                        HObject imageCopy = null;
                        HOperatorSet.CopyObj(Image, out imageCopy, 1, 1);

                        // 使用线程安全的并发队列
                        imageQueue.Enqueue(imageCopy);
                        Interlocked.Increment(ref queuedFrames);
                        lastImageTime = DateTime.Now;

                        // 检查是否需要动态增加处理线程
                        CheckAndScaleProcessingThreads();

                        // 定期记录队列状态（每100帧记录一次）
                        if (capturedFrames % 100 == 0)
                        {
                            LogMessage($"队列状态：已捕获{capturedFrames}帧，队列中{queuedFrames}帧，处理线程{processingThreads}个");
                        }
                    }
                    catch (Exception ex)
                    {
                        LogMessage($"图像入队失败：{ex.Message}");
                        // 即使入队失败也不丢弃，尝试直接处理
                        Task.Run(() => ProcessImageDirectly(Image));
                    }
                }
            }
            catch (HalconException exc)
            {
                // 线程安全的错误处理 - 使用日志记录而不是MessageBox
                LogMessage($"HALCON Error: {exc.Message}");
            }
           
        }


        public void DisplayImage(HObject Image, HSmartWindowControl control)
        {
            try
            {
                if (Image == null || !Image.IsInitialized())
                {
                    MessageBox.Show("图像为空或未初始化，无法显示。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                if (control.HalconWindow == null)
                {
                    MessageBox.Show("HALCON 控件未初始化。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }
                // 获取图像尺寸
                HOperatorSet.GetImageSize(Image, out HTuple width, out HTuple height);

                // 设置窗口坐标系，适应图像大小
                HOperatorSet.SetPart(hSmartWindowControl1.HalconWindow, 0, 0, height - 1, width - 1);

                // 显示图像
                HOperatorSet.DispObj(Image, hSmartWindowControl1.HalconWindow);


                //HOperatorSet.DispObj(Image, control.HalconWindow);
            }
            catch (HOperatorException ex)
            {
                MessageBox.Show($"显示图像时发生异常：{ex.Message}", "异常", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void CameraGrabberSaveImageComplete(
            IntPtr Grabber,
            IntPtr image,	// 需要调用CameraImage_Destroy释放
            CameraSdkStatus Status,
            IntPtr Context)
        {
            if (image == IntPtr.Zero || Status != CameraSdkStatus.CAMERA_STATUS_SUCCESS)
            {
                ShowError("Invalid image or camera status error.");
                return;
            }

        }
        private void ShowError(string message)
        {
            if (this.InvokeRequired)
            {
                this.Invoke((MethodInvoker)(() =>
                    MessageBox.Show(message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error)));
            }
            else
            {
                MessageBox.Show(message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ListView1_SelectedIndexChanged(object sender, EventArgs e)
        {

        }
        //相机设置
        private void buttonSettings_Click(object sender, EventArgs e)
        {
            if (m_Grabber != IntPtr.Zero)
                MvApi.CameraShowSettingPage(m_hCamera, 1);
        }

        private async Task GracefullyStopProcessingAsync(int timeoutSeconds = 5)
        {
            try
            {
                // 先取消任务
                cancellationTokenSource?.Cancel();
                LogMessage("已发送停止信号到所有处理线程");
                
                // 创建一个超时任务
                var timeoutTask = Task.Delay(TimeSpan.FromSeconds(timeoutSeconds));
                
                // 创建一个等待所有处理任务的任务
                var allTasksCompletionTask = Task.WhenAll(processingTasks);
                
                // 等待任务完成或超时
                await Task.WhenAny(allTasksCompletionTask, timeoutTask);
                
                if (!allTasksCompletionTask.IsCompleted)
                {
                    LogMessage($"等待处理线程停止超时（{timeoutSeconds}秒），某些线程可能仍在运行");
                }
                else
                {
                    LogMessage("所有处理线程已正常停止");
                }
                
                // 清空任务列表
                processingTasks.Clear();
            }
            catch (Exception ex)
            {
                LogMessage($"停止处理线程时出错: {ex.Message}");
            }
        }

        private async void btnStart_Click_1(object sender, EventArgs e)
        {
            if (isProcessing)
            {
                // 禁用按钮，防止重复点击
                btnStart.Enabled = false;
                btnStart.Text = "正在停止...";
                
                // 如果正在处理，则暂停 - 使用优雅停止方法
                await GracefullyStopProcessingAsync(3); // 使用3秒超时
                
                isProcessing = false;
                
                // 重新启用按钮
                btnStart.Enabled = true;
                btnStart.Text = "开始";
                LogMessage("图像处理已停止");
            }
            else
            {
                isProcessing = true;
                cancellationTokenSource = new CancellationTokenSource();
                processingTasks.Clear();

                // 启动初始处理线程
                for (int i = 0; i < processingThreads; i++)
                {
                    int threadId = i; // 捕获循环变量
                    var task = ProcessImagesThread(threadId, cancellationTokenSource.Token);
                    processingTasks.Add(task);
                }

                btnStart.Text = "暂停";
                LogMessage($"图像处理已启动，初始线程数：{processingThreads}");
            }
        }

        // 动态检查并扩展处理线程
        private void CheckAndScaleProcessingThreads()
        {
            if (!isProcessing || cancellationTokenSource == null || cancellationTokenSource.Token.IsCancellationRequested)
                return;

            // 计算队列积压情况
            int currentQueueSize = queuedFrames;

            // 如果队列积压超过1000帧且线程数未达到最大值，增加线程
            if (currentQueueSize > 1000 && processingThreads < maxProcessingThreads)
            {
                int newThreadId = processingThreads;
                processingThreads++;

                var task = ProcessImagesThread(newThreadId, cancellationTokenSource.Token);
                processingTasks.Add(task);

                LogMessage($"队列积压{currentQueueSize}帧，自动增加处理线程到{processingThreads}个");
            }
        }

        // 直接处理图像（当队列操作失败时的备用方案）
        private void ProcessImageDirectly(HObject image)
        {
            try
            {
                if (image != null && image.IsInitialized())
                {
                    HObject imageCopy = null;
                    HOperatorSet.CopyObj(image, out imageCopy, 1, 1);

                    Interlocked.Increment(ref processedFrames);
                    ProcessImage(imageCopy);

                    imageCopy?.Dispose();
                    LogMessage("图像已通过直接处理方式完成");
                }
            }
            catch (Exception ex)
            {
                LogMessage($"直接处理图像失败：{ex.Message}");
            }
        }

        // 高效实时处理线程 - 绝不丢弃图像
        private async Task ProcessImagesThread(int threadId, CancellationToken cancellationToken)
        {
            LogMessage($"高性能处理线程 {threadId} 已启动");
            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    HObject ho_Image = null;
                    bool hasImage = false;

                    try
                    {
                        // 检查取消状态，提高取消响应速度
                        if (cancellationToken.IsCancellationRequested)
                            break;

                        // 从并发队列中取出图像，添加超时控制
                        hasImage = imageQueue.TryDequeue(out ho_Image);

                        if (hasImage && ho_Image != null && ho_Image.IsInitialized())
                        {
                            // 减少队列计数
                            Interlocked.Decrement(ref queuedFrames);

                            // 增加处理帧计数
                            Interlocked.Increment(ref processedFrames);

                            // 处理图像（不记录详细日志以提高性能）
                            var startTime = DateTime.Now;
                            
                            // 再次检查取消状态，如已取消则不处理图像
                            if (!cancellationToken.IsCancellationRequested)
                            {
                                ProcessImage(ho_Image);
                            }
                            
                            var processingTime = DateTime.Now - startTime;

                            // 只在处理时间过长时记录日志
                            if (processingTime.TotalMilliseconds > 100)
                            {
                                LogMessage($"线程 {threadId} 处理耗时较长：{processingTime.TotalMilliseconds:F0}ms");
                            }
                        }
                        else
                        {
                            // 队列为空时使用更短的休眠时间，并快速检查取消状态
                            await Task.Delay(5, cancellationToken);
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        // 正常的取消操作，退出循环
                        break;
                    }
                    catch (HOperatorException ex)
                    {
                        // HALCON错误，记录但继续处理
                        LogMessage($"线程 {threadId} HALCON错误：{ex.Message}");
                        // 不增加丢失计数，因为我们没有丢弃图像
                    }
                    catch (Exception ex)
                    {
                        // 其他错误，记录但继续处理
                        LogMessage($"线程 {threadId} 处理错误：{ex.Message}");
                        // 不增加丢失计数，因为我们没有丢弃图像
                    }
                    finally
                    {
                        // 释放图像资源
                        ho_Image?.Dispose();
                    }
                }
            }
            catch (Exception ex)
            {
                LogMessage($"处理线程 {threadId} 异常终止：{ex.Message}");
            }
            finally
            {
                LogMessage($"处理线程 {threadId} 已停止");
            }
        }

        private void ProcessImage(HObject ho_Image)
        {
            if (ho_Image == null || !ho_Image.IsInitialized())
            {
                LogMessage("处理图像时出错: 输入图像为空或未初始化");
                return;
            }
            HObject ho_GrayImage, ho_ImageMean, ho_Regions, ho_RegionOpening;
            HObject ho_ConnectedRegions, ho_SelectedRegions, ho_RegionTrans;
            HObject ho_SortedRegions, ho_Crosses, ho_Cross = null;

            // Local control variables 

            HTuple hv_Pointer = new HTuple(), hv_Type = new HTuple();
            HTuple hv_Width = new HTuple(), hv_Height = new HTuple();
            HTuple hv_WindowHandle = new HTuple(), hv_Area = new HTuple();
            HTuple hv_CenterX = new HTuple(), hv_CenterY = new HTuple();
            HTuple hv_CrossSize = new HTuple(), hv_NumPoints = new HTuple();
            HTuple hv_Index = new HTuple(), hv_Distances = new HTuple();
            HTuple hv_Distance = new HTuple(), hv_TotalDistance = new HTuple();
            HTuple hv_NumDistances = new HTuple(), hv_MeanDistance = new HTuple();
            HTuple hv_Tolerance = new HTuple(), hv_IsUniform = new HTuple();
            HTuple hv_Difference = new HTuple();


            HOperatorSet.GenEmptyObj(out ho_GrayImage);
            HOperatorSet.GenEmptyObj(out ho_ImageMean);
            HOperatorSet.GenEmptyObj(out ho_Regions);
            HOperatorSet.GenEmptyObj(out ho_RegionOpening);
            HOperatorSet.GenEmptyObj(out ho_ConnectedRegions);
            HOperatorSet.GenEmptyObj(out ho_SelectedRegions);
            HOperatorSet.GenEmptyObj(out ho_RegionTrans);
            HOperatorSet.GenEmptyObj(out ho_SortedRegions);
            HOperatorSet.GenEmptyObj(out ho_Crosses);
            HOperatorSet.GenEmptyObj(out ho_Cross);
            //Image Acquisition 01: Code generated by Image Acquisition 01
            hv_Pointer.Dispose(); hv_Type.Dispose(); hv_Width.Dispose(); hv_Height.Dispose();


            ho_GrayImage.Dispose();
            HOperatorSet.Rgb1ToGray(ho_Image, out ho_GrayImage);

            ho_ImageMean.Dispose();
            HOperatorSet.MeanImage(ho_GrayImage, out ho_ImageMean, 5, 5);

            ho_Regions.Dispose();
            HOperatorSet.Threshold(ho_ImageMean, out ho_Regions, 180, 255);





            ho_RegionOpening.Dispose();
            HOperatorSet.OpeningCircle(ho_Regions, out ho_RegionOpening, 18);



            ho_ConnectedRegions.Dispose();
            HOperatorSet.Connection(ho_RegionOpening, out ho_ConnectedRegions);




            ho_SelectedRegions.Dispose();
            HOperatorSet.SelectShape(ho_ConnectedRegions, out ho_SelectedRegions, (((new HTuple("area")).TupleConcat(
                "height")).TupleConcat("width")).TupleConcat("circularity"), "and", (((new HTuple(2500)).TupleConcat(
                50)).TupleConcat(50)).TupleConcat(0.2), (((new HTuple(15000)).TupleConcat(
                160)).TupleConcat(160)).TupleConcat(0.75202));




            ho_RegionTrans.Dispose();
            HOperatorSet.ShapeTrans(ho_SelectedRegions, out ho_RegionTrans, "rectangle2");


            ho_SortedRegions.Dispose();
            HOperatorSet.SortRegion(ho_RegionTrans, out ho_SortedRegions, "first_point",
                "false", "column");
            hv_Area.Dispose(); hv_CenterX.Dispose(); hv_CenterY.Dispose();
            HOperatorSet.AreaCenter(ho_SortedRegions, out hv_Area, out hv_CenterX, out hv_CenterY);



            hv_CrossSize.Dispose();
            hv_CrossSize = 5;
            HOperatorSet.SetLineWidth(hv_ExpDefaultWinHandle, 5);

            HOperatorSet.SetColor(hv_ExpDefaultWinHandle, "yellow");
            ho_Crosses.Dispose();
            HOperatorSet.GenEmptyObj(out ho_Crosses);

            hv_NumPoints.Dispose();




            using (HDevDisposeHelper dh = new HDevDisposeHelper())
            {
                hv_NumPoints = new HTuple(hv_CenterY.TupleLength()
                    );
            }
            if ((int)(new HTuple(hv_NumPoints.TupleLess(0))) != 0)
            {
                // 线程安全的消息显示
                LogMessage("请调整相机 - 检测到的点数不足");
                return; // 提前返回，避免后续处理
            }
            else
            {

                HTuple end_val41 = hv_NumPoints - 2;


                HTuple step_val41 = 1;
                for (hv_Index = 1; hv_Index.Continue(end_val41, step_val41); hv_Index = hv_Index.TupleAdd(step_val41))
                {
                    //为每个中心点生成一个十字标记



                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        ho_Cross.Dispose();
                        HOperatorSet.GenCrossContourXld(out ho_Cross, hv_CenterX.TupleSelect(hv_Index),
                            hv_CenterY.TupleSelect(hv_Index), 10, 0);
                    }
                    {
                        HObject ExpTmpOutVar_0;
                        // 将当前标记点添加到对象列表中
                        HOperatorSet.ConcatObj(ho_Crosses, ho_Cross, out ExpTmpOutVar_0);
                        ho_Crosses.Dispose();
                        ho_Crosses = ExpTmpOutVar_0;

                        if (ExpTmpOutVar_0 != null)
                        {
                            // 线程安全的UI更新 - 使用Invoke调用UI线程
                            if (hSmartWindowControl2.InvokeRequired)
                            {
                                hSmartWindowControl2.Invoke(new Action(() =>
                                {
                                    try
                                    {
                                        // 设置窗口坐标系，适应图像大小
                                        HOperatorSet.GetImageSize(ho_Image, out HTuple width, out HTuple height);
                                        HOperatorSet.SetPart(hSmartWindowControl2.HalconWindow, 0, 0, height, width);
                                        HOperatorSet.SetLineWidth(hSmartWindowControl2.HalconWindow, 5);
                                        HOperatorSet.SetColor(hSmartWindowControl2.HalconWindow, "red");
                                        HOperatorSet.DispObj(ExpTmpOutVar_0, hSmartWindowControl2.HalconWindow);
                                    }
                                    catch (Exception ex)
                                    {
                                        LogMessage($"显示检测结果时出错：{ex.Message}");
                                    }
                                }));
                            }
                            else
                            {
                                try
                                {
                                    // 设置窗口坐标系，适应图像大小
                                    HOperatorSet.GetImageSize(ho_Image, out HTuple width, out HTuple height);
                                    HOperatorSet.SetPart(hSmartWindowControl2.HalconWindow, 0, 0, height, width);
                                    HOperatorSet.SetLineWidth(hSmartWindowControl2.HalconWindow, 5);
                                    HOperatorSet.SetColor(hSmartWindowControl2.HalconWindow, "red");
                                    HOperatorSet.DispObj(ExpTmpOutVar_0, hSmartWindowControl2.HalconWindow);
                                }
                                catch (Exception ex)
                                {
                                    LogMessage($"显示检测结果时出错：{ex.Message}");
                                }
                            }
                        }
                        else
                        {
                            // 线程安全的错误消息显示
                            if (this.InvokeRequired)
                            {
                                this.BeginInvoke(new Action(() =>
                                {
                                    LogMessage("显示对象无效或为空！");
                                }));
                            }
                            else
                            {
                                LogMessage("显示对象无效或为空！");
                            }
                        }
                    }

                    hv_Distance.Dispose();
                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        hv_Distance = (((((hv_CenterX.TupleSelect(
                            hv_Index + 1)) - (hv_CenterX.TupleSelect(hv_Index))) * ((hv_CenterX.TupleSelect(
                            hv_Index + 1)) - (hv_CenterX.TupleSelect(hv_Index)))) + (((hv_CenterY.TupleSelect(
                            hv_Index + 1)) - (hv_CenterY.TupleSelect(hv_Index))) * ((hv_CenterY.TupleSelect(
                            hv_Index + 1)) - (hv_CenterY.TupleSelect(hv_Index)))))).TupleSqrt();
                    }

                    if (hv_Distances == null)
                        hv_Distances = new HTuple();
                    hv_Distances[hv_Index] = hv_Distance;
                }

            }
            hv_TotalDistance.Dispose();
            hv_TotalDistance = 0;
            hv_NumDistances.Dispose();
            using (HDevDisposeHelper dh = new HDevDisposeHelper())
            {
                hv_NumDistances = new HTuple(hv_Distances.TupleLength()
                    );
            }

            //计算距离总和
            HTuple end_val62 = hv_NumPoints - 2;
            HTuple step_val62 = 1;
            for (hv_Index = 1; hv_Index.Continue(end_val62, step_val62); hv_Index = hv_Index.TupleAdd(step_val62))
            {
                using (HDevDisposeHelper dh = new HDevDisposeHelper())
                {
                    {
                        HTuple
                          ExpTmpLocalVar_TotalDistance = hv_TotalDistance + (hv_Distances.TupleSelect(
                            hv_Index));
                        hv_TotalDistance.Dispose();
                        hv_TotalDistance = ExpTmpLocalVar_TotalDistance;
                    }
                }
            }

            //计算平均距离
            hv_MeanDistance.Dispose();
            using (HDevDisposeHelper dh = new HDevDisposeHelper())
            {
                // 检查除数是否为0
                if (hv_NumDistances == null || (int)(new HTuple(hv_NumDistances.TupleEqual(0))) != 0)
                {
                    // 如果没有有效的距离，设置为默认值
                    hv_MeanDistance = 0;

                    // 使用BeginInvoke避免跨线程UI更新问题
                    if (MeasureDistLabel.InvokeRequired)
                    {
                        MeasureDistLabel.BeginInvoke(new Action(() =>
                        {
                            MeasureDistLabel.Text = "线间距: 无有效数据";
                        }));
                    }
                    else
                    {
                        MeasureDistLabel.Text = "线间距: 无有效数据";
                    }
                }
                else
                {
                    // 正常情况下安全地执行除法
                    hv_MeanDistance = hv_TotalDistance / hv_NumDistances;

                    // 使用BeginInvoke更新UI
                    MeasureDistLabel.BeginInvoke(new Action(() =>
                    {
                        MeasureDistLabel.Text = "线间距: " + String.Format("{0:F2}", hv_MeanDistance.D);
                        //绘图
                        chart1.ChartAreas[0].AxisX.LabelStyle.Enabled = false;
                        chart1.Series[0].Points.AddXY(DateTime.Now, hv_MeanDistance.D);
                        if (chart1.Series[0].Points.Count > 100) // 最多显示100个点
                        {
                            chart1.Series[0].Points.RemoveAt(0);
                        }
                        chart1.ChartAreas[0].RecalculateAxesScale(); // 自动缩放坐标轴
                        chart1.ChartAreas[0].AxisY.Interval = 20;   // 设置 Y 轴刻度为 20
                    }));
                }
            }
            hv_Tolerance.Dispose();


            using (HDevDisposeHelper dh = new HDevDisposeHelper())
            {

                if (float.TryParse(textBox1.Text, out float Num_Multiplier))
                {
                    // 成功转换，执行乘法并设置 hv_Tolerance
                    hv_Tolerance = Num_Multiplier * hv_MeanDistance;

                    // 如果您想要更新另一个控件（例如 label）以显示结果，可以这样做：
                    //label1.Text = hv_Tolerance.ToString();
                }
                else
                {
                    // 处理输入不是有效数字的情况 - 线程安全的日志记录
                    LogMessage("容差设置无效：请输入有效的数字");
                    return; // 直接返回，终止方法的执行
                }
            }
            hv_IsUniform.Dispose();
            hv_IsUniform = 1;

            HTuple end_val73 = hv_NumPoints - 2;
            HTuple step_val73 = 1;
            for (hv_Index = 1; hv_Index.Continue(end_val73, step_val73); hv_Index = hv_Index.TupleAdd(step_val73))
            {

                //计算绝对值
                hv_Difference.Dispose();
                using (HDevDisposeHelper dh = new HDevDisposeHelper())
                {
                    hv_Difference = (hv_Distances.TupleSelect(
                        hv_Index)) - hv_MeanDistance;
                }
                if ((int)(new HTuple(hv_Difference.TupleLess(0))) != 0)
                {
                    using (HDevDisposeHelper dh = new HDevDisposeHelper())
                    {
                        {
                            HTuple
                              ExpTmpLocalVar_Difference = -hv_Difference;
                            hv_Difference.Dispose();
                            hv_Difference = ExpTmpLocalVar_Difference;
                        }
                    }
                }

                if ((int)(new HTuple(hv_Difference.TupleGreater(hv_Tolerance))) != 0)
                {
                    hv_IsUniform.Dispose();
                    hv_IsUniform = 0;
                    break;
                }
                //MeasureDistLabel.BeginInvoke(new Action(() =>
                //{at("{0:F2}", hv_IsUniform.D);
                //}));
                //    label1.Text = String.Form
            }
            try
            {

                // 检查是否均匀
                if ((int)(hv_IsUniform) != 0)
                {

                    LogMessage("检测结果：绕线均匀");
                    MvApi.CameraSetIOState(m_hCamera, 1, 1);
                    // 使用安全的方法更新UI控件
                    UpdateRadioButtonState(true);
                }
                else
                {
                    MvApi.CameraSetIOState(m_hCamera, 1, 0);
                    // 设置高电平
                    LogMessage("检测结果：绕线不均匀");

                    // 使用安全的方法更新UI控件
                    UpdateRadioButtonState(false);

                    Task.Run(async () =>
                    {
                        await Task.Delay(5000); // 延时 5 秒
                        MvApi.CameraSetIOState(m_hCamera, 1, 1); // 复位为低电平
                        LogMessage("已复位，输出低电平");
                        // 复位后安全地更新UI控件状态
                        UpdateRadioButtonState(true);
                    });





                    // 获取当前日期和时间，并格式化为字符串
                    string dateTime = DateTime.Now.ToString("yyyyMMdd_HHmmss");

                    // 设置保存路径为 D:\save\，并确保目录存在
                    string savePath = @"D:\save";
                    if (!Directory.Exists(savePath))
                    {
                        Directory.CreateDirectory(savePath);
                    }

                    // 设置保存文件的路径和文件名，带有时间戳
                    string filename = Path.Combine(savePath, $"螺旋线不均匀缠绕_{dateTime}.png");

                    // 确保图像已初始化并有效
                    if (ho_Image != null && ho_Image.IsInitialized())
                    {
                        try
                        {
                            // 锁定图像资源以避免多线程访问冲突
                            lock (imageLock)
                            {
                                // 保存图像为 PNG 格式
                                HOperatorSet.WriteImage(ho_Image, "png", 0, filename);
                                LogMessage($"不良图像已保存：{filename}");
                                Console.WriteLine($"图像已保存为：{filename}", "提示", MessageBoxButtons.OK, MessageBoxIcon.Information);
                            }
                        }
                        catch (Exception saveEx)
                        {
                            LogMessage($"保存图像时出错：{saveEx.Message}");
                            Console.WriteLine($"保存图像时出错：{saveEx.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                    else
                    {
                        LogMessage("图像未正确加载或初始化，无法保存。");
                        Console.WriteLine("图像未正确加载或初始化，无法保存。", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }



                }
            }
            catch (Exception ex)
            {
                // 线程安全的错误处理 - 只使用日志记录
                LogMessage($"检测出错：{ex.Message}");
            }
            // 资源释放（应放在最外层 try 的末尾）

            ho_GrayImage.Dispose();
            ho_ImageMean.Dispose();
            ho_Regions.Dispose();
            ho_RegionOpening.Dispose();
            ho_ConnectedRegions.Dispose();
            ho_SelectedRegions.Dispose();
            ho_RegionTrans.Dispose();
            ho_SortedRegions.Dispose();
            ho_Crosses.Dispose();
            ho_Cross.Dispose();

            hv_Pointer.Dispose();
            hv_Type.Dispose();
            hv_Width.Dispose();
            hv_Height.Dispose();
            hv_WindowHandle.Dispose();
            hv_Area.Dispose();
            hv_CenterX.Dispose();
            hv_CenterY.Dispose();
            hv_CrossSize.Dispose();
            hv_NumPoints.Dispose();
            hv_Index.Dispose();
            hv_Distances.Dispose();
            hv_Distance.Dispose();
            hv_TotalDistance.Dispose();
            hv_NumDistances.Dispose();
            hv_MeanDistance.Dispose();
            hv_Tolerance.Dispose();
            hv_IsUniform.Dispose();
            hv_Difference.Dispose();



        }


        private void MeasureDistLabel_Click(object sender, EventArgs e)
        {

        }

        private void StateLabel_Click(object sender, EventArgs e)
        {

        }
       
        private void InitializeMonitoring()
        {
            monitorTimer = new FormsTimer();
            monitorTimer.Interval = 2000; // 2秒监控间隔，专注实时性能
            monitorTimer.Tick += (s, e) => {

                // 计算实时处理速率
                var now = DateTime.Now;
                var timeDiff = (now - lastPerformanceCheck).TotalSeconds;
                if (timeDiff >= 2.0) // 每2秒计算一次处理速率
                {
                    int currentProcessed = processedFrames;
                    int processedInInterval = currentProcessed - lastProcessedCount;
                    currentProcessingRate = processedInInterval / timeDiff;

                    lastPerformanceCheck = now;
                    lastProcessedCount = currentProcessed;
                }

                // 获取当前队列状态
                int currentQueueSize = queuedFrames;

                // 计算处理效率
                double processingEfficiency = capturedFrames > 0 ? (processedFrames / (double)capturedFrames * 100) : 0;

                // 实时性能报告
                LogMessage($"实时状态：捕获{capturedFrames}帧，处理{processedFrames}帧，队列{currentQueueSize}帧");
                LogMessage($"处理性能：效率{processingEfficiency:F1}%，速率{currentProcessingRate:F1}帧/秒，线程{processingThreads}个");

                // 内存使用检查
                long memoryUsageMB = GC.GetTotalMemory(false) / (1024 * 1024);
                if (memoryUsageMB > maxMemoryUsageMB * 0.8)
                {
                    LogMessage($"内存警告：当前使用{memoryUsageMB}MB，接近限制{maxMemoryUsageMB}MB");
                    // 强制垃圾回收
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                }

                // 队列积压检查
                if (currentQueueSize > 2000)
                {
                    LogMessage($"队列积压警告：{currentQueueSize}帧待处理，正在自动扩展处理能力");
                    CheckAndScaleProcessingThreads();
                }

                // 实时性检查：如果队列积压超过5秒的处理量，发出警告
                double estimatedProcessingTime = currentQueueSize / Math.Max(currentProcessingRate, 1);
                if (estimatedProcessingTime > 5.0)
                {
                    LogMessage($"实时性警告：预计需要{estimatedProcessingTime:F1}秒处理完当前队列");
                }

                // 检查图像采集连续性
                TimeSpan timeSinceLastImage = DateTime.Now - lastImageTime;
                if (timeSinceLastImage.TotalSeconds > 5) // 超过5秒没有新图像
                {
                    LogMessage($"采集警告：已{timeSinceLastImage.TotalSeconds:F0}秒未收到新图像");
                }
            };
            monitorTimer.Start();
            LogMessage("实时性能监控已启动");
        }


        private void SetupSoftwareTrigger()
        {
            // 根据物体速度和视场计算合适的触发间隔
            int triggerIntervalMs = 50; // 示例：每50ms触发一次
            
            captureTimer = new System.Timers.Timer(triggerIntervalMs);
            captureTimer.Elapsed += (s, e) => {
                if (m_hCamera != 0) // 移除暂停判断
                {
                    // 软件触发一帧图像
                    MvApi.CameraSoftTrigger(m_hCamera);
                }
            };
            captureTimer.AutoReset = true;
            captureTimer.Enabled = true;
        }

        // 获取实时队列状态信息
        private string GetQueueStatus()
        {
            int currentQueueSize = queuedFrames;
            double processingRate = capturedFrames > 0 ? (processedFrames / (double)capturedFrames * 100) : 0;
            long memoryUsageMB = GC.GetTotalMemory(false) / (1024 * 1024);

            return $"实时状态 - 队列:{currentQueueSize}帧, 处理率:{processingRate:F1}%, " +
                   $"速率:{currentProcessingRate:F1}帧/秒, 线程:{processingThreads}个, 内存:{memoryUsageMB}MB";
        }

        // 添加新的辅助方法来安全地更新RadioButton状态
        private void UpdateRadioButtonState(bool lowChecked)
        {
            if (rbLow.InvokeRequired || rbHigh.InvokeRequired)
            {
                // 使用BeginInvoke避免死锁
                this.BeginInvoke(new Action(() =>
                {
                    rbLow.Checked = lowChecked;
                    rbHigh.Checked = !lowChecked;
                }));
            }
            else
            {
                rbLow.Checked = lowChecked;
                rbHigh.Checked = !lowChecked;
            }
        }
    }

}