﻿namespace winding
{
    partial class Form1
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要修改
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.Windows.Forms.DataVisualization.Charting.ChartArea chartArea1 = new System.Windows.Forms.DataVisualization.Charting.ChartArea();
            System.Windows.Forms.DataVisualization.Charting.Legend legend1 = new System.Windows.Forms.DataVisualization.Charting.Legend();
            System.Windows.Forms.DataVisualization.Charting.Series series1 = new System.Windows.Forms.DataVisualization.Charting.Series();
            this.buttonSettings = new System.Windows.Forms.Button();
            this.listViewLog = new System.Windows.Forms.ListView();
            this.StateLabel = new System.Windows.Forms.Label();
            this.MeasureDistLabel = new System.Windows.Forms.Label();
            this.Timer1 = new System.Windows.Forms.Timer(this.components);
            this.btnStart = new System.Windows.Forms.Button();
            this.textBox1 = new System.Windows.Forms.TextBox();
            this.label2 = new System.Windows.Forms.Label();
            this.label1 = new System.Windows.Forms.Label();
            this.chart1 = new System.Windows.Forms.DataVisualization.Charting.Chart();
            this.gbGpio = new System.Windows.Forms.GroupBox();
            this.rbHigh = new System.Windows.Forms.RadioButton();
            this.rbLow = new System.Windows.Forms.RadioButton();
            this.hSmartWindowControl1 = new HalconDotNet.HSmartWindowControl();
            this.hSmartWindowControl2 = new HalconDotNet.HSmartWindowControl();
            this.groupBox1 = new System.Windows.Forms.GroupBox();
            this.rbHigh2 = new System.Windows.Forms.RadioButton();
            this.rbLow2 = new System.Windows.Forms.RadioButton();
            ((System.ComponentModel.ISupportInitialize)(this.chart1)).BeginInit();
            this.gbGpio.SuspendLayout();
            this.groupBox1.SuspendLayout();
            this.SuspendLayout();
            // 
            // buttonSettings
            // 
            this.buttonSettings.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.buttonSettings.Location = new System.Drawing.Point(872, 73);
            this.buttonSettings.Name = "buttonSettings";
            this.buttonSettings.Size = new System.Drawing.Size(88, 67);
            this.buttonSettings.TabIndex = 3;
            this.buttonSettings.Text = "相机设置";
            this.buttonSettings.UseVisualStyleBackColor = true;
            this.buttonSettings.Click += new System.EventHandler(this.buttonSettings_Click);
            // 
            // listViewLog
            // 
            this.listViewLog.HideSelection = false;
            this.listViewLog.Location = new System.Drawing.Point(861, 304);
            this.listViewLog.Name = "listViewLog";
            this.listViewLog.Size = new System.Drawing.Size(306, 263);
            this.listViewLog.TabIndex = 12;
            this.listViewLog.UseCompatibleStateImageBehavior = false;
            this.listViewLog.View = System.Windows.Forms.View.Details;
            this.listViewLog.SelectedIndexChanged += new System.EventHandler(this.ListView1_SelectedIndexChanged);
            // 
            // StateLabel
            // 
            this.StateLabel.AutoSize = true;
            this.StateLabel.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.StateLabel.Location = new System.Drawing.Point(12, 167);
            this.StateLabel.Name = "StateLabel";
            this.StateLabel.Size = new System.Drawing.Size(55, 16);
            this.StateLabel.TabIndex = 13;
            this.StateLabel.Text = "状态栏";
            this.StateLabel.Click += new System.EventHandler(this.StateLabel_Click);
            // 
            // MeasureDistLabel
            // 
            this.MeasureDistLabel.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.MeasureDistLabel.Location = new System.Drawing.Point(869, 16);
            this.MeasureDistLabel.Name = "MeasureDistLabel";
            this.MeasureDistLabel.Size = new System.Drawing.Size(135, 26);
            this.MeasureDistLabel.TabIndex = 26;
            this.MeasureDistLabel.Text = "线间距:";
            this.MeasureDistLabel.TextAlign = System.Drawing.ContentAlignment.MiddleLeft;
            this.MeasureDistLabel.Click += new System.EventHandler(this.MeasureDistLabel_Click);
            // 
            // Timer1
            // 
            this.Timer1.Enabled = true;
            this.Timer1.Interval = 1000;
            this.Timer1.Tick += new System.EventHandler(this.Timer1_Tick_1);
            // 
            // btnStart
            // 
            this.btnStart.Font = new System.Drawing.Font("宋体", 10.5F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.btnStart.Location = new System.Drawing.Point(1004, 73);
            this.btnStart.Name = "btnStart";
            this.btnStart.Size = new System.Drawing.Size(88, 60);
            this.btnStart.TabIndex = 29;
            this.btnStart.Text = "开始采集";
            this.btnStart.UseVisualStyleBackColor = true;
            this.btnStart.Click += new System.EventHandler(this.btnStart_Click_1);
            // 
            // textBox1
            // 
            this.textBox1.Location = new System.Drawing.Point(1097, 21);
            this.textBox1.Name = "textBox1";
            this.textBox1.Size = new System.Drawing.Size(54, 21);
            this.textBox1.TabIndex = 31;
            this.textBox1.Text = "0.4";
            // 
            // label2
            // 
            this.label2.AutoSize = true;
            this.label2.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label2.Location = new System.Drawing.Point(1016, 21);
            this.label2.Name = "label2";
            this.label2.Size = new System.Drawing.Size(75, 16);
            this.label2.TabIndex = 33;
            this.label2.Text = "倍率参数";
            // 
            // label1
            // 
            this.label1.AutoSize = true;
            this.label1.Font = new System.Drawing.Font("宋体", 12F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(134)));
            this.label1.Location = new System.Drawing.Point(7, 350);
            this.label1.Name = "label1";
            this.label1.Size = new System.Drawing.Size(55, 16);
            this.label1.TabIndex = 34;
            this.label1.Text = "中心点";
            // 
            // chart1
            // 
            this.chart1.BackColor = System.Drawing.SystemColors.Window;
            chartArea1.AxisX.LabelAutoFitMaxFontSize = 15;
            chartArea1.AxisX.LabelAutoFitMinFontSize = 8;
            chartArea1.AxisX.MajorGrid.Enabled = false;
            chartArea1.AxisX.MajorGrid.IntervalOffset = 0D;
            chartArea1.AxisX.MajorGrid.IntervalType = System.Windows.Forms.DataVisualization.Charting.DateTimeIntervalType.Auto;
            chartArea1.AxisX.MajorGrid.LineColor = System.Drawing.Color.Transparent;
            chartArea1.AxisX.MajorTickMark.Enabled = false;
            chartArea1.AxisX.MajorTickMark.Interval = 0D;
            chartArea1.AxisX.MajorTickMark.IntervalOffsetType = System.Windows.Forms.DataVisualization.Charting.DateTimeIntervalType.Auto;
            chartArea1.AxisX.ScaleView.SmallScrollMinSize = 10D;
            chartArea1.AxisY.IsLabelAutoFit = false;
            chartArea1.AxisY.IsStartedFromZero = false;
            chartArea1.AxisY.LabelAutoFitMaxFontSize = 30;
            chartArea1.AxisY.MajorGrid.Enabled = false;
            chartArea1.AxisY.MajorTickMark.TickMarkStyle = System.Windows.Forms.DataVisualization.Charting.TickMarkStyle.None;
            chartArea1.Name = "ChartArea1";
            this.chart1.ChartAreas.Add(chartArea1);
            legend1.Enabled = false;
            legend1.Name = "Legend1";
            this.chart1.Legends.Add(legend1);
            this.chart1.Location = new System.Drawing.Point(84, 339);
            this.chart1.Name = "chart1";
            this.chart1.Palette = System.Windows.Forms.DataVisualization.Charting.ChartColorPalette.SemiTransparent;
            series1.ChartArea = "ChartArea1";
            series1.ChartType = System.Windows.Forms.DataVisualization.Charting.SeriesChartType.Line;
            series1.Legend = "Legend1";
            series1.Name = "Series1";
            this.chart1.Series.Add(series1);
            this.chart1.Size = new System.Drawing.Size(735, 240);
            this.chart1.TabIndex = 35;
            this.chart1.Text = "chart1";
            // 
            // gbGpio
            // 
            this.gbGpio.Controls.Add(this.rbHigh);
            this.gbGpio.Controls.Add(this.rbLow);
            this.gbGpio.Location = new System.Drawing.Point(861, 167);
            this.gbGpio.Name = "gbGpio";
            this.gbGpio.Size = new System.Drawing.Size(93, 110);
            this.gbGpio.TabIndex = 36;
            this.gbGpio.TabStop = false;
            this.gbGpio.Text = "GPIO状态";
            // 
            // rbHigh
            // 
            this.rbHigh.AutoSize = true;
            this.rbHigh.ForeColor = System.Drawing.Color.Red;
            this.rbHigh.Location = new System.Drawing.Point(24, 73);
            this.rbHigh.Name = "rbHigh";
            this.rbHigh.Size = new System.Drawing.Size(59, 16);
            this.rbHigh.TabIndex = 1;
            this.rbHigh.Text = "高电平";
            this.rbHigh.UseVisualStyleBackColor = true;
            // 
            // rbLow
            // 
            this.rbLow.AutoSize = true;
            this.rbLow.Checked = true;
            this.rbLow.ForeColor = System.Drawing.Color.Lime;
            this.rbLow.Location = new System.Drawing.Point(24, 37);
            this.rbLow.Name = "rbLow";
            this.rbLow.Size = new System.Drawing.Size(59, 16);
            this.rbLow.TabIndex = 0;
            this.rbLow.TabStop = true;
            this.rbLow.Text = "低电平";
            this.rbLow.UseVisualStyleBackColor = true;
            // 
            // hSmartWindowControl1
            // 
            this.hSmartWindowControl1.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.hSmartWindowControl1.AutoValidate = System.Windows.Forms.AutoValidate.EnableAllowFocusChange;
            this.hSmartWindowControl1.HDoubleClickToFitContent = true;
            this.hSmartWindowControl1.HDrawingObjectsModifier = HalconDotNet.HSmartWindowControl.DrawingObjectsModifier.None;
            this.hSmartWindowControl1.HImagePart = new System.Drawing.Rectangle(0, 0, 640, 480);
            this.hSmartWindowControl1.HKeepAspectRatio = true;
            this.hSmartWindowControl1.HMoveContent = true;
            this.hSmartWindowControl1.HZoomContent = HalconDotNet.HSmartWindowControl.ZoomContent.WheelForwardZoomsIn;
            this.hSmartWindowControl1.Location = new System.Drawing.Point(1, 12);
            this.hSmartWindowControl1.Margin = new System.Windows.Forms.Padding(0);
            this.hSmartWindowControl1.Name = "hSmartWindowControl1";
            this.hSmartWindowControl1.Size = new System.Drawing.Size(829, 155);
            this.hSmartWindowControl1.TabIndex = 37;
            this.hSmartWindowControl1.WindowSize = new System.Drawing.Size(829, 155);
            // 
            // hSmartWindowControl2
            // 
            this.hSmartWindowControl2.AutoSizeMode = System.Windows.Forms.AutoSizeMode.GrowAndShrink;
            this.hSmartWindowControl2.AutoValidate = System.Windows.Forms.AutoValidate.EnableAllowFocusChange;
            this.hSmartWindowControl2.HDoubleClickToFitContent = true;
            this.hSmartWindowControl2.HDrawingObjectsModifier = HalconDotNet.HSmartWindowControl.DrawingObjectsModifier.None;
            this.hSmartWindowControl2.HImagePart = new System.Drawing.Rectangle(0, 0, 640, 480);
            this.hSmartWindowControl2.HKeepAspectRatio = true;
            this.hSmartWindowControl2.HMoveContent = true;
            this.hSmartWindowControl2.HZoomContent = HalconDotNet.HSmartWindowControl.ZoomContent.WheelForwardZoomsIn;
            this.hSmartWindowControl2.Location = new System.Drawing.Point(6, 186);
            this.hSmartWindowControl2.Margin = new System.Windows.Forms.Padding(0);
            this.hSmartWindowControl2.Name = "hSmartWindowControl2";
            this.hSmartWindowControl2.Size = new System.Drawing.Size(823, 143);
            this.hSmartWindowControl2.TabIndex = 38;
            this.hSmartWindowControl2.WindowSize = new System.Drawing.Size(823, 143);
            // 
            // groupBox1
            // 
            this.groupBox1.Controls.Add(this.rbHigh2);
            this.groupBox1.Controls.Add(this.rbLow2);
            this.groupBox1.Location = new System.Drawing.Point(1004, 167);
            this.groupBox1.Name = "groupBox1";
            this.groupBox1.Size = new System.Drawing.Size(93, 110);
            this.groupBox1.TabIndex = 39;
            this.groupBox1.TabStop = false;
            this.groupBox1.Text = "位置信号";
            // 
            // rbHigh2
            // 
            this.rbHigh2.AutoSize = true;
            this.rbHigh2.ForeColor = System.Drawing.Color.Red;
            this.rbHigh2.Location = new System.Drawing.Point(24, 73);
            this.rbHigh2.Name = "rbHigh2";
            this.rbHigh2.Size = new System.Drawing.Size(59, 16);
            this.rbHigh2.TabIndex = 1;
            this.rbHigh2.Text = "高电平";
            this.rbHigh2.UseVisualStyleBackColor = true;
            // 
            // rbLow2
            // 
            this.rbLow2.AutoSize = true;
            this.rbLow2.Checked = true;
            this.rbLow2.ForeColor = System.Drawing.Color.Lime;
            this.rbLow2.Location = new System.Drawing.Point(24, 37);
            this.rbLow2.Name = "rbLow2";
            this.rbLow2.Size = new System.Drawing.Size(59, 16);
            this.rbLow2.TabIndex = 0;
            this.rbLow2.TabStop = true;
            this.rbLow2.Text = "低电平";
            this.rbLow2.UseVisualStyleBackColor = true;
            // 
            // Form1
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(6F, 12F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.AutoSize = true;
            this.ClientSize = new System.Drawing.Size(1197, 579);
            this.Controls.Add(this.groupBox1);
            this.Controls.Add(this.hSmartWindowControl2);
            this.Controls.Add(this.hSmartWindowControl1);
            this.Controls.Add(this.gbGpio);
            this.Controls.Add(this.chart1);
            this.Controls.Add(this.label1);
            this.Controls.Add(this.label2);
            this.Controls.Add(this.textBox1);
            this.Controls.Add(this.btnStart);
            this.Controls.Add(this.MeasureDistLabel);
            this.Controls.Add(this.StateLabel);
            this.Controls.Add(this.listViewLog);
            this.Controls.Add(this.buttonSettings);
            this.ImeMode = System.Windows.Forms.ImeMode.On;
            this.Location = new System.Drawing.Point(10, 10);
            this.Name = "Form1";
            this.Text = "Form1";
            this.Load += new System.EventHandler(this.Form1_Load);
            ((System.ComponentModel.ISupportInitialize)(this.chart1)).EndInit();
            this.gbGpio.ResumeLayout(false);
            this.gbGpio.PerformLayout();
            this.groupBox1.ResumeLayout(false);
            this.groupBox1.PerformLayout();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion
        private System.Windows.Forms.Button buttonSettings;
        private System.Windows.Forms.ListView listViewLog;
        private System.Windows.Forms.Label StateLabel;
        internal System.Windows.Forms.Label MeasureDistLabel;
        private System.Windows.Forms.Timer Timer1;
        private System.Windows.Forms.Button btnStart;
        private System.Windows.Forms.TextBox textBox1;
        private System.Windows.Forms.Label label2;
        private System.Windows.Forms.Label label1;
        public System.Windows.Forms.DataVisualization.Charting.Chart chart1;
        private System.Windows.Forms.GroupBox gbGpio;
        private System.Windows.Forms.RadioButton rbHigh;
        private System.Windows.Forms.RadioButton rbLow;
        private HalconDotNet.HSmartWindowControl hSmartWindowControl1;
        private HalconDotNet.HSmartWindowControl hSmartWindowControl2;
        private System.Windows.Forms.GroupBox groupBox1;
        private System.Windows.Forms.RadioButton rbHigh2;
        private System.Windows.Forms.RadioButton rbLow2;
    }
}

